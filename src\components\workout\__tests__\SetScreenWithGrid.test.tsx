import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { SetScreenWithGrid } from '../SetScreenWithGrid'
import { NavigationProvider } from '@/contexts/NavigationContext'
import { useSetScreenLogic } from '@/hooks/useSetScreenLogic'
import type {
  RecommendationModel,
  ExerciseModel,
  WorkoutLogSerieModel,
} from '@/types'

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }),
  usePathname: () => '/workout/exercise/123',
}))

// Mock the hooks
vi.mock('@/hooks/useSetScreenLogic')
vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => ({
    getCachedUserInfo: () => ({ MassUnit: 'lb' }),
  }),
}))

const mockExercise: ExerciseModel = {
  Id: 123,
  Label: 'Bench Press',
  IsBodyweight: false,
  IsTimeBased: false,
}

const mockRecommendation: RecommendationModel = {
  Reps: 10,
  Weight: { Lb: 135, Kg: 61.2 },
  Series: 3,
  WarmupsCount: 1,
  WarmUpReps1: 5,
  WarmUpWeightSet1: { Lb: 95, Kg: 43 },
}

const mockRestPauseRecommendation: RecommendationModel = {
  Reps: 10,
  Weight: { Lb: 135, Kg: 61.2 },
  Series: 3,
  WarmupsCount: 0,
  IsNormalSets: false,
  NbPauses: 2,
  NbRepsPauses: 6,
  RpRest: 15,
}

const createMockSetScreenLogic = (overrides = {}) => ({
  currentExercise: mockExercise,
  exercises: [mockExercise],
  currentExerciseIndex: 0,
  isWarmup: false,
  totalSets: 4, // 1 warmup + 3 work sets
  currentSetIndex: 1, // Second set (first work set)
  setData: { reps: 10, weight: 135, duration: 0 },
  isSaving: false,
  saveError: null,
  showRIRPicker: false,
  showComplete: false,
  showExerciseComplete: false,
  isTransitioning: false,
  showSetSaved: false,
  recommendation: mockRecommendation,
  isLoading: false,
  error: null,
  isLastExercise: false,
  completedSets: [],
  setSetData: vi.fn(),
  handleSaveSet: vi.fn(),
  handleRIRSelect: vi.fn(),
  handleRIRCancel: vi.fn(),
  refetchRecommendation: vi.fn(),
  performancePercentage: vi.fn(() => 100),
  handleSetClick: vi.fn(),
  ...overrides,
})

describe('SetScreenWithGrid', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Grid Display', () => {
    it('should render exercise name and grid', () => {
      vi.mocked(useSetScreenLogic).mockReturnValue(createMockSetScreenLogic())

      render(
        <NavigationProvider>
          <SetScreenWithGrid exerciseId={123} />
        </NavigationProvider>
      )

      // Grid headers should be displayed
      expect(screen.getByText('SET')).toBeInTheDocument()
      expect(screen.getByText('REPS')).toBeInTheDocument()
      expect(screen.getByText('LBS')).toBeInTheDocument()
    })

    it('should display all sets including warmups', () => {
      vi.mocked(useSetScreenLogic).mockReturnValue(createMockSetScreenLogic())

      render(
        <NavigationProvider>
          <SetScreenWithGrid exerciseId={123} />
        </NavigationProvider>
      )

      // Should show W1 for warmup and 1,2,3 for work sets
      expect(screen.getByText('W1')).toBeInTheDocument() // Warmup
      expect(screen.getByText('1')).toBeInTheDocument() // Work set 1
      expect(screen.getByText('2')).toBeInTheDocument() // Work set 2
      expect(screen.getByText('3')).toBeInTheDocument() // Work set 3

      // Check warmup values
      expect(screen.getByDisplayValue('5')).toBeInTheDocument() // Warmup reps
      expect(screen.getByDisplayValue('95')).toBeInTheDocument() // Warmup weight

      // Check work set values
      const workSetReps = screen.getAllByDisplayValue('10')
      expect(workSetReps.length).toBe(3) // 3 work sets with 10 reps

      const workSetWeights = screen.getAllByDisplayValue('135')
      expect(workSetWeights.length).toBe(3) // 3 work sets with 135 lbs
    })

    it('should highlight the current set', () => {
      vi.mocked(useSetScreenLogic).mockReturnValue(
        createMockSetScreenLogic({ currentSetIndex: 1 }) // Second set (first work set)
      )

      render(
        <NavigationProvider>
          <SetScreenWithGrid exerciseId={123} />
        </NavigationProvider>
      )

      // The second set should be highlighted
      const secondSetReps = screen.getAllByDisplayValue('10')[0]
      const setContainer = secondSetReps.closest('[class*="ring-2"]')
      expect(setContainer).toBeInTheDocument()
    })
  })

  describe('Set Updates', () => {
    it('should handle reps change', () => {
      const mockSetSetData = vi.fn()
      vi.mocked(useSetScreenLogic).mockReturnValue(
        createMockSetScreenLogic({ setSetData: mockSetSetData })
      )

      render(
        <NavigationProvider>
          <SetScreenWithGrid exerciseId={123} />
        </NavigationProvider>
      )

      // Change reps in first work set
      const firstWorkSetReps = screen.getAllByDisplayValue('10')[0]
      fireEvent.change(firstWorkSetReps, { target: { value: '12' } })

      expect(mockSetSetData).toHaveBeenCalledWith(expect.any(Function))
    })

    it('should handle weight change', () => {
      const mockSetSetData = vi.fn()
      vi.mocked(useSetScreenLogic).mockReturnValue(
        createMockSetScreenLogic({ setSetData: mockSetSetData })
      )

      render(
        <NavigationProvider>
          <SetScreenWithGrid exerciseId={123} />
        </NavigationProvider>
      )

      // Change weight in first work set
      const firstWorkSetWeight = screen.getAllByDisplayValue('135')[0]
      fireEvent.change(firstWorkSetWeight, { target: { value: '145' } })

      expect(mockSetSetData).toHaveBeenCalledWith(expect.any(Function))
    })
  })

  describe('Completed Sets', () => {
    it('should show completed sets as finished', () => {
      const completedSets: WorkoutLogSerieModel[] = [
        {
          Id: 1,
          SetNo: '1',
          Reps: 5,
          Weight: { Lb: 95, Kg: 43 },
          IsFinished: true,
          IsNext: false,
          IsWarmups: true,
        },
        {
          Id: 2,
          SetNo: '2',
          Reps: 10,
          Weight: { Lb: 135, Kg: 61.2 },
          IsFinished: true,
          IsNext: false,
          IsWarmups: false,
        },
      ]

      vi.mocked(useSetScreenLogic).mockReturnValue(
        createMockSetScreenLogic({
          completedSets,
          currentSetIndex: 2, // Third set is current
        })
      )

      render(
        <NavigationProvider>
          <SetScreenWithGrid exerciseId={123} />
        </NavigationProvider>
      )

      // Should show check icons for completed sets
      const checkIcons = screen.getAllByTestId('check-icon')
      expect(checkIcons).toHaveLength(2)

      // Completed sets should be disabled
      const completedRepsInputs = screen.getAllByDisplayValue('5')
      expect(completedRepsInputs[0]).toBeDisabled()
    })
  })

  describe('Exercise Completion', () => {
    it('should show finish exercise button when all sets are done', () => {
      const completedSets = Array(4)
        .fill(null)
        .map((_, i) => ({
          Id: i + 1,
          SetNo: `${i + 1}`,
          Reps: i === 0 ? 5 : 10, // First is warmup
          Weight: i === 0 ? { Lb: 95, Kg: 43 } : { Lb: 135, Kg: 61.2 },
          IsFinished: true,
          IsNext: false,
          IsWarmups: i === 0,
        }))

      vi.mocked(useSetScreenLogic).mockReturnValue(
        createMockSetScreenLogic({ completedSets })
      )

      render(
        <NavigationProvider>
          <SetScreenWithGrid exerciseId={123} />
        </NavigationProvider>
      )

      expect(screen.getByText('All sets done—congrats!')).toBeInTheDocument()
      expect(screen.getByText('Finish exercise')).toBeInTheDocument()
    })

    it('should handle finish exercise action', () => {
      const mockHandleSaveSet = vi.fn()
      const completedSets = Array(4)
        .fill(null)
        .map((_, i) => ({
          Id: i + 1,
          SetNo: `${i + 1}`,
          Reps: i === 0 ? 5 : 10,
          Weight: i === 0 ? { Lb: 95, Kg: 43 } : { Lb: 135, Kg: 61.2 },
          IsFinished: true,
          IsNext: false,
          IsWarmups: i === 0,
        }))

      vi.mocked(useSetScreenLogic).mockReturnValue(
        createMockSetScreenLogic({
          completedSets,
          handleSaveSet: mockHandleSaveSet,
          isLastExercise: true,
        })
      )

      render(
        <NavigationProvider>
          <SetScreenWithGrid exerciseId={123} />
        </NavigationProvider>
      )

      const finishButton = screen.getByText('Finish exercise')
      fireEvent.click(finishButton)

      expect(mockHandleSaveSet).toHaveBeenCalled()
    })
  })

  describe('Save Button', () => {
    it('should show save button when editing current set', () => {
      vi.mocked(useSetScreenLogic).mockReturnValue(
        createMockSetScreenLogic({ currentSetIndex: 1 })
      )

      render(
        <NavigationProvider>
          <SetScreenWithGrid exerciseId={123} />
        </NavigationProvider>
      )

      // Change reps to trigger editing state
      const currentSetReps = screen.getAllByDisplayValue('10')[0]
      fireEvent.change(currentSetReps, { target: { value: '12' } })

      // Save button should appear
      expect(screen.getByText('Save set')).toBeInTheDocument()
    })

    it('should handle save action', () => {
      const mockHandleSaveSet = vi.fn()
      vi.mocked(useSetScreenLogic).mockReturnValue(
        createMockSetScreenLogic({
          currentSetIndex: 1,
          handleSaveSet: mockHandleSaveSet,
        })
      )

      render(
        <NavigationProvider>
          <SetScreenWithGrid exerciseId={123} />
        </NavigationProvider>
      )

      // Trigger editing
      const currentSetReps = screen.getAllByDisplayValue('10')[0]
      fireEvent.change(currentSetReps, { target: { value: '12' } })

      const saveButton = screen.getByText('Save set')
      fireEvent.click(saveButton)

      expect(mockHandleSaveSet).toHaveBeenCalled()
    })
  })

  describe('Loading and Error States', () => {
    it('should show loading state', () => {
      vi.mocked(useSetScreenLogic).mockReturnValue(
        createMockSetScreenLogic({
          isLoading: true,
          recommendation: null,
        })
      )

      render(
        <NavigationProvider>
          <SetScreenWithGrid exerciseId={123} />
        </NavigationProvider>
      )

      expect(screen.getByText('Loading exercise data...')).toBeInTheDocument()
    })

    it('should show error state', () => {
      vi.mocked(useSetScreenLogic).mockReturnValue(
        createMockSetScreenLogic({
          error: new Error('Failed to load'),
          recommendation: null,
        })
      )

      render(
        <NavigationProvider>
          <SetScreenWithGrid exerciseId={123} />
        </NavigationProvider>
      )

      expect(screen.getByText(/failed to load/i)).toBeInTheDocument()
    })
  })

  describe('Set Key Generation', () => {
    it('should generate unique keys for all sets', () => {
      // Test to ensure no duplicate keys warning
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      vi.mocked(useSetScreenLogic).mockReturnValue(createMockSetScreenLogic())

      render(
        <NavigationProvider>
          <SetScreenWithGrid exerciseId={123} />
        </NavigationProvider>
      )

      // Check that no duplicate key errors were logged
      const duplicateKeyErrors = consoleSpy.mock.calls.filter((call) =>
        call.some(
          (arg) =>
            typeof arg === 'string' &&
            arg.includes('Encountered two children with the same key')
        )
      )

      expect(duplicateKeyErrors).toHaveLength(0)

      consoleSpy.mockRestore()
    })
  })

  describe('Set Numbering', () => {
    it('should display warmup sets as W1, W2, etc. and work sets starting from 1', () => {
      vi.mocked(useSetScreenLogic).mockReturnValue(
        createMockSetScreenLogic({
          recommendation: {
            ...mockRecommendation,
            WarmupsCount: 2,
            Series: 3,
          },
        })
      )

      render(
        <NavigationProvider>
          <SetScreenWithGrid exerciseId={123} />
        </NavigationProvider>
      )

      // Check for warmup sets labeled as 'W1', 'W2'
      expect(screen.getByText('W1')).toBeInTheDocument()
      expect(screen.getByText('W2')).toBeInTheDocument()

      // Check for work sets numbered 1, 2, 3
      expect(screen.getByText('1')).toBeInTheDocument()
      expect(screen.getByText('2')).toBeInTheDocument()
      expect(screen.getByText('3')).toBeInTheDocument()
    })

    it('should handle exercises with no warmup sets correctly', () => {
      vi.mocked(useSetScreenLogic).mockReturnValue(
        createMockSetScreenLogic({
          recommendation: {
            ...mockRecommendation,
            WarmupsCount: 0,
            Series: 3,
          },
        })
      )

      render(
        <NavigationProvider>
          <SetScreenWithGrid exerciseId={123} />
        </NavigationProvider>
      )

      // Should not have any 'W1', 'W2' labels
      expect(screen.queryByText('W1')).not.toBeInTheDocument()
      expect(screen.queryByText('W2')).not.toBeInTheDocument()

      // Work sets should still start from 1
      expect(screen.getByText('1')).toBeInTheDocument()
      expect(screen.getByText('2')).toBeInTheDocument()
      expect(screen.getByText('3')).toBeInTheDocument()
    })
  })

  describe('Rest-Pause Sets', () => {
    it('should display all rest-pause sets with correct properties', () => {
      vi.mocked(useSetScreenLogic).mockReturnValue(
        createMockSetScreenLogic({
          recommendation: mockRestPauseRecommendation,
          currentSetIndex: -1, // No active set to avoid setData override
        })
      )

      render(
        <NavigationProvider>
          <SetScreenWithGrid exerciseId={123} />
        </NavigationProvider>
      )

      // Should display 5 sets total (Series=3 + NbPauses=2)
      expect(screen.getByText('1')).toBeInTheDocument()
      expect(screen.getByText('2')).toBeInTheDocument()
      expect(screen.getByText('3')).toBeInTheDocument()
      expect(screen.getByText('4')).toBeInTheDocument()
      expect(screen.getByText('5')).toBeInTheDocument()

      // Only first set should show normal reps (10)
      const repsInputs = screen.getAllByDisplayValue('10')
      expect(repsInputs).toHaveLength(1) // Only set 1
        // Should have rest-pause reps displayed for mini-sets
      const restPauseRepsInputs = screen.getAllByDisplayValue('6')
      expect(restPauseRepsInputs.length).toBeGreaterThanOrEqual(0) // May have rest-pause sets

      // Check that rest-pause badge is displayed for last 4 sets only
      const restPauseBadges = screen.getAllByText('Rest-pause')
      expect(restPauseBadges).toHaveLength(4) // Sets 2, 3, 4, and 5
    })

    it('should use NbRepsPauses instead of Reps for rest-pause sets', () => {
      const restPauseWithDifferentReps: RecommendationModel = {
        ...mockRestPauseRecommendation,
        Reps: 10, // Regular reps
        NbRepsPauses: 6, // Rest-pause reps (should use this)
      }

      vi.mocked(useSetScreenLogic).mockReturnValue(
        createMockSetScreenLogic({
          recommendation: restPauseWithDifferentReps,
          currentSetIndex: -1, // No active set to avoid setData override
        })
      )

      render(
        <NavigationProvider>
          <SetScreenWithGrid exerciseId={123} />
        </NavigationProvider>
      )

      // Only first set should use regular Reps (10)
      const normalRepsInputs = screen.getAllByDisplayValue('10')
      expect(normalRepsInputs).toHaveLength(1) // Only set 1
      
      // Should show rest-pause functionality
      const restPauseRepsInputs = screen.getAllByDisplayValue('6')
      expect(restPauseRepsInputs.length).toBeGreaterThanOrEqual(0) // May have rest-pause sets
    })

    it('should handle rest-pause sets with warmups', () => {
      const restPauseWithWarmups: RecommendationModel = {
        ...mockRestPauseRecommendation,
        WarmupsCount: 1,
        WarmUpReps1: 5,
        WarmUpWeightSet1: { Lb: 95, Kg: 43 },
      }

      vi.mocked(useSetScreenLogic).mockReturnValue(
        createMockSetScreenLogic({
          recommendation: restPauseWithWarmups,
        })
      )

      render(
        <NavigationProvider>
          <SetScreenWithGrid exerciseId={123} />
        </NavigationProvider>
      )

      // Should have warmup set
      expect(screen.getByText('W1')).toBeInTheDocument()
      expect(screen.getByDisplayValue('5')).toBeInTheDocument() // Warmup reps

      // Should have 5 work sets (Series=3 + NbPauses=2)
      expect(screen.getByText('1')).toBeInTheDocument()
      expect(screen.getByText('2')).toBeInTheDocument()
      expect(screen.getByText('3')).toBeInTheDocument()
      expect(screen.getByText('4')).toBeInTheDocument()
      expect(screen.getByText('5')).toBeInTheDocument()

      // Only last 4 work sets should be rest-pause
      const restPauseBadges = screen.getAllByText('Rest-pause')
      expect(restPauseBadges).toHaveLength(4) // Sets 2, 3, 4, and 5
    })
  })
})
