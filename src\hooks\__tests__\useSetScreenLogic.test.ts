import { renderHook, act, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import { useSetScreenLogic } from '../useSetScreenLogic'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useRIR } from '@/hooks/useRIR'
import type { RecommendationModel, ExerciseModel } from '@/types'

// Create mock router push function outside to track calls
const mockRouterPush = vi.fn()

// Mock dependencies
vi.mock('@/hooks/useWorkout')
vi.mock('@/stores/workoutStore')
vi.mock('@/hooks/useRIR')
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockRouterPush,
    replace: vi.fn(),
  }),
}))

describe('useSetScreenLogic - Rest-Pause Sets', () => {
  const mockExercise: ExerciseModel = {
    Id: 123,
    Label: 'Bench Press',
    IsBodyweight: false,
    IsTimeBased: false,
    Timer: 45,
    BodyPartId: 1,
    EquipmentId: 1,
    IsUnilateral: false,
    IsFlexibility: false,
    IsAssisted: false,
    VideoUrl: '',
    IsNormalSets: false,
  }

  const mockRestPauseRecommendation: RecommendationModel = {
    ExerciseId: 123,
    Series: 1, // 1 main work set
    Reps: 12,
    Weight: { Lb: 135, Kg: 61.2 },
    NbPauses: 3, // 3 rest-pause mini-sets
    NbRepsPauses: 8,
    RpRest: 15,
    IsNormalSets: false,
    IsPyramid: false,
    IsReversePyramid: false,
    WarmupsCount: 2,
    WarmUpReps1: 5,
    WarmUpReps2: 3,
    WarmUpWeightSet1: { Lb: 95, Kg: 43.1 },
    WarmUpWeightSet2: { Lb: 115, Kg: 52.2 },
    WarmUpsList: [],
    OneRMProgress: 0,
    RecommendationInKg: 61.2,
    OneRMPercentage: 75,
    IsBodyweight: false,
    IsEasy: false,
    IsMedium: true,
    Increments: { Lb: 5, Kg: 2.5 },
    Max: { Lb: 500, Kg: 227 },
    Min: { Lb: 45, Kg: 20 },
    IsDeload: false,
    IsBackOffSet: false,
    BackOffSetWeight: { Lb: 115, Kg: 52.2 },
    IsMaxChallenge: false,
    IsLightSession: false,
    FirstWorkSetReps: 12,
    FirstWorkSetWeight: { Lb: 135, Kg: 61.2 },
    FirstWorkSet1RM: { Lb: 180, Kg: 81.6 },
    MinReps: 8,
    MaxReps: 12,
    IsDropSet: false,
    IsPlate: false,
    IsTimeBased: false,
    RIR: 2,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    mockRouterPush.mockClear()

    // Mock useWorkout
    vi.mocked(useWorkout).mockReturnValue({
      saveSet: vi.fn().mockResolvedValue(undefined),
      isLoading: false,
      error: null,
      getRecommendation: vi.fn().mockResolvedValue(mockRestPauseRecommendation),
    } as any)

    // Mock useWorkoutStore
    vi.mocked(useWorkoutStore).mockReturnValue({
      exercises: [mockExercise],
      currentExerciseIndex: 0,
      currentSetIndex: 2, // After warmups (2), on first work set
      workoutSession: {
        id: 'test-session',
        startTime: new Date(),
        exercises: [],
      },
      nextSet: vi.fn(),
      setCurrentSetIndex: vi.fn(),
      nextExercise: vi.fn(),
      setCurrentExerciseById: vi.fn(),
      getCachedExerciseRecommendation: vi.fn().mockReturnValue(null),
    } as any)

    // Mock useRIR
    vi.mocked(useRIR).mockReturnValue({
      mapRIRValueToNumber: vi.fn((v) => parseInt(v)),
      saveRIR: vi.fn().mockResolvedValue(undefined),
    } as any)
  })

  it('should NOT mark exercise as complete after saving first work set of rest-pause exercise', async () => {
    const { result } = renderHook(() => useSetScreenLogic(123))

    // Wait for recommendation to load
    await waitFor(() => {
      expect(result.current.recommendation).toBeTruthy()
    })

    // Verify initial state
    expect(result.current.currentSetIndex).toBe(2) // First work set (after 2 warmups)
    expect(result.current.isFirstWorkSet).toBe(true)

    // CRITICAL TEST: isLastSet should be false because we have rest-pause mini-sets
    // Total sets = warmups (2) + work sets (1) + rest-pause mini-sets (3) = 6
    // Current index is 2, last set index would be 5
    expect(result.current.isLastSet).toBe(false) // This should fail with current implementation
    expect(result.current.totalSets).toBe(6) // This should fail with current implementation

    // Save the first work set
    await act(async () => {
      await result.current.handleSaveSet()
    })

    // Should show RIR picker (since it's first work set)
    expect(result.current.showRIRPicker).toBe(true)

    // Exercise should NOT be marked as complete
    expect(result.current.showExerciseComplete).toBe(false)
  })

  it('should correctly identify last set when on final rest-pause mini-set', async () => {
    // Mock being on the last rest-pause mini-set
    vi.mocked(useWorkoutStore).mockReturnValue({
      exercises: [mockExercise],
      currentExerciseIndex: 0,
      currentSetIndex: 5, // Last rest-pause mini-set (2 warmups + 1 work + 3 rest-pause - 1)
      workoutSession: {
        id: 'test-session',
        startTime: new Date(),
        exercises: [],
      },
      nextSet: vi.fn(),
      setCurrentSetIndex: vi.fn(),
      nextExercise: vi.fn(),
      setCurrentExerciseById: vi.fn(),
      getCachedExerciseRecommendation: vi.fn().mockReturnValue(null),
    } as any)

    const { result } = renderHook(() => useSetScreenLogic(123))

    await waitFor(() => {
      expect(result.current.recommendation).toBeTruthy()
    })

    // Should correctly identify this as the last set
    expect(result.current.isLastSet).toBe(true)
  })

  it('should handle normal sets correctly (unchanged behavior)', async () => {
    const normalRecommendation = {
      ...mockRestPauseRecommendation,
      IsNormalSets: true,
      Series: 3,
      NbPauses: 0,
    }

    vi.mocked(useWorkout).mockReturnValue({
      saveSet: vi.fn().mockResolvedValue(undefined),
      isLoading: false,
      error: null,
      getRecommendation: vi.fn().mockResolvedValue(normalRecommendation),
    } as any)

    vi.mocked(useWorkoutStore).mockReturnValue({
      exercises: [mockExercise],
      currentExerciseIndex: 0,
      currentSetIndex: 4, // Last work set (2 warmups + 2 done + on 3rd)
      workoutSession: {
        id: 'test-session',
        startTime: new Date(),
        exercises: [],
      },
      nextSet: vi.fn(),
      setCurrentSetIndex: vi.fn(),
      nextExercise: vi.fn(),
      setCurrentExerciseById: vi.fn(),
      getCachedExerciseRecommendation: vi.fn().mockReturnValue(null),
    } as any)

    const { result } = renderHook(() => useSetScreenLogic(123))

    await waitFor(() => {
      expect(result.current.recommendation).toBeTruthy()
    })

    // For normal sets, totalSets should be warmups + work sets only
    expect(result.current.totalSets).toBe(5) // 2 warmups + 3 work sets
    expect(result.current.isLastSet).toBe(true)
  })
})

describe('useSetScreenLogic - Warmup Set Data Updates', () => {
  const mockExercise: ExerciseModel = {
    Id: 123,
    Label: 'Bench Press',
    IsBodyweight: false,
    IsTimeBased: false,
    Timer: 45,
    BodyPartId: 1,
    EquipmentId: 1,
    IsUnilateral: false,
    IsFlexibility: false,
    IsAssisted: false,
    VideoUrl: '',
    IsNormalSets: true,
  }

  const mockRecommendation: RecommendationModel = {
    ExerciseId: 123,
    Series: 3,
    Reps: 10,
    Weight: { Lb: 135, Kg: 61.2 },
    NbPauses: 0,
    IsNormalSets: true,
    IsPyramid: false,
    IsReversePyramid: false,
    WarmupsCount: 2,
    WarmUpReps1: 5,
    WarmUpReps2: 3,
    WarmUpWeightSet1: { Lb: 45, Kg: 20.4 },
    WarmUpWeightSet2: { Lb: 95, Kg: 43.1 },
    WarmUpsList: [],
    FirstWorkSetReps: 10,
    FirstWorkSetWeight: { Lb: 135, Kg: 61.2 },
    OneRMProgress: 0,
    RecommendationInKg: 61.2,
    OneRMPercentage: 75,
    IsBodyweight: false,
    IsEasy: false,
    IsMedium: true,
    Increments: { Lb: 5, Kg: 2.5 },
    Max: { Lb: 500, Kg: 227 },
    Min: { Lb: 45, Kg: 20 },
    IsDeload: false,
    IsBackOffSet: false,
    BackOffSetWeight: { Lb: 115, Kg: 52.2 },
    IsMaxChallenge: false,
    IsLightSession: false,
    MinReps: 8,
    MaxReps: 12,
    IsDropSet: false,
    IsPlate: false,
    IsTimeBased: false,
    RIR: 2,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    mockRouterPush.mockClear()

    // Mock useWorkout
    vi.mocked(useWorkout).mockReturnValue({
      saveSet: vi.fn().mockResolvedValue(undefined),
      isLoading: false,
      error: null,
      getRecommendation: vi.fn().mockResolvedValue(mockRecommendation),
    } as any)

    // Mock useRIR
    vi.mocked(useRIR).mockReturnValue({
      mapRIRValueToNumber: vi.fn((v) => parseInt(v)),
      saveRIR: vi.fn().mockResolvedValue(undefined),
    } as any)
  })

  it('should update setData when currentSetIndex changes to reflect warmup set values', async () => {
    const mockNextSet = vi.fn()

    // Start on first warmup set
    const initialStore = {
      exercises: [mockExercise],
      currentExerciseIndex: 0,
      currentSetIndex: 0, // First warmup
      workoutSession: {
        id: 'test-session',
        startTime: new Date(),
        exercises: [],
      },
      nextSet: mockNextSet,
      setCurrentSetIndex: vi.fn(),
      nextExercise: vi.fn(),
      setCurrentExerciseById: vi.fn(),
      getCachedExerciseRecommendation: vi
        .fn()
        .mockReturnValue(mockRecommendation),
    }

    vi.mocked(useWorkoutStore).mockReturnValue(initialStore as any)

    const { result, rerender } = renderHook(() => useSetScreenLogic(123))

    // Wait for recommendation to load
    await waitFor(() => {
      expect(result.current.recommendation).toBeTruthy()
    })

    // Verify initial state shows first warmup set data
    expect(result.current.setData.reps).toBe(5) // WarmUpReps1
    expect(result.current.setData.weight).toBe(45) // WarmUpWeightSet1.Lb

    // Simulate moving to second warmup set by updating store
    vi.mocked(useWorkoutStore).mockReturnValue({
      ...initialStore,
      currentSetIndex: 1, // Second warmup
    } as any)

    // Rerender to simulate store update
    rerender()

    // Test expectation: setData should update to show second warmup values
    // This will fail initially because the hook doesn't watch currentSetIndex changes
    await waitFor(() => {
      expect(result.current.setData.reps).toBe(3) // WarmUpReps2
      expect(result.current.setData.weight).toBe(95) // WarmUpWeightSet2.Lb
    })
  })

  it('should clear setData values when moving between warmup sets after save', async () => {
    const mockSaveSet = vi.fn().mockResolvedValue(undefined)
    vi.mocked(useWorkout).mockReturnValue({
      saveSet: mockSaveSet,
      isLoading: false,
      error: null,
      getRecommendation: vi.fn().mockResolvedValue(mockRecommendation),
    } as any)

    // Start on first warmup with some entered values
    const initialStore = {
      exercises: [mockExercise],
      currentExerciseIndex: 0,
      currentSetIndex: 0, // First warmup
      workoutSession: {
        id: 'test-session',
        startTime: new Date(),
        exercises: [],
      },
      nextSet: vi.fn(),
      setCurrentSetIndex: vi.fn(),
      nextExercise: vi.fn(),
      setCurrentExerciseById: vi.fn(),
      getCachedExerciseRecommendation: vi
        .fn()
        .mockReturnValue(mockRecommendation),
    }

    vi.mocked(useWorkoutStore).mockReturnValue(initialStore as any)

    const { result, rerender } = renderHook(() => useSetScreenLogic(123))

    await waitFor(() => {
      expect(result.current.recommendation).toBeTruthy()
    })

    // User modifies the values
    act(() => {
      result.current.setSetData({ reps: 8, weight: 55, duration: 45 })
    })

    expect(result.current.setData.reps).toBe(8)
    expect(result.current.setData.weight).toBe(55)

    // Save the set
    await act(async () => {
      await result.current.handleSaveSet()
    })

    // Simulate store update after nextSet is called
    vi.mocked(useWorkoutStore).mockReturnValue({
      ...initialStore,
      currentSetIndex: 1, // Moved to second warmup
    } as any)

    rerender()

    // Test expectation: setData should show second warmup's default values, not the previous set's values
    await waitFor(() => {
      expect(result.current.setData.reps).toBe(3) // WarmUpReps2, not 8
      expect(result.current.setData.weight).toBe(95) // WarmUpWeightSet2.Lb, not 55
    })
  })
})

describe('useSetScreenLogic - Immediate Navigation', () => {
  const mockExercise: ExerciseModel = {
    Id: 123,
    Label: 'Bench Press',
    IsBodyweight: false,
    IsTimeBased: false,
    Timer: 45,
    BodyPartId: 1,
    EquipmentId: 1,
    IsUnilateral: false,
    IsFlexibility: false,
    IsAssisted: false,
    VideoUrl: '',
    IsNormalSets: true,
  }

  const mockRecommendation: RecommendationModel = {
    ExerciseId: 123,
    Series: 3,
    Reps: 10,
    Weight: { Lb: 135, Kg: 61.2 },
    NbPauses: 0,
    IsNormalSets: true,
    IsPyramid: false,
    IsReversePyramid: false,
    WarmupsCount: 2,
    WarmUpReps1: 5,
    WarmUpWeightSet1: { Lb: 95, Kg: 43.1 },
    WarmUpsList: [],
    FirstWorkSetReps: 10,
    FirstWorkSetWeight: { Lb: 135, Kg: 61.2 },
  } as RecommendationModel

  beforeEach(() => {
    vi.clearAllMocks()
    mockRouterPush.mockClear()
    vi.useFakeTimers()

    // Mock useWorkout
    vi.mocked(useWorkout).mockReturnValue({
      saveSet: vi.fn().mockResolvedValue(undefined),
      isLoading: false,
      error: null,
      getRecommendation: vi.fn().mockResolvedValue(mockRecommendation),
    } as any)

    // Mock useWorkoutStore
    vi.mocked(useWorkoutStore).mockReturnValue({
      exercises: [mockExercise],
      currentExerciseIndex: 0,
      currentSetIndex: 3, // Second work set (after 2 warmups + 1 work set)
      workoutSession: {
        id: 'test-session',
        startTime: new Date(),
        exercises: [],
      },
      nextSet: vi.fn(),
      setCurrentSetIndex: vi.fn(),
      nextExercise: vi.fn(),
      setCurrentExerciseById: vi.fn(),
      getCachedExerciseRecommendation: vi.fn().mockReturnValue(null),
    } as any)

    // Mock useRIR
    vi.mocked(useRIR).mockReturnValue({
      mapRIRValueToNumber: vi.fn((v) => parseInt(v)),
      saveRIR: vi.fn().mockResolvedValue(undefined),
    } as any)
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('should navigate to rest timer immediately without 600ms delay', async () => {
    const { result } = renderHook(() => useSetScreenLogic(123))

    // Wait for recommendation to load
    await waitFor(() => {
      expect(result.current.recommendation).toBeTruthy()
    })

    // Clear any previous calls
    mockRouterPush.mockClear()

    // Save a set (not the last set, not first work set)
    await act(async () => {
      result.current.handleSaveSet()
    })

    // The test expects immediate navigation, but current implementation has 600ms delay
    // So this should fail initially
    expect(mockRouterPush).not.toHaveBeenCalled()

    // Advance time by 600ms - this is when navigation currently happens
    act(() => {
      vi.advanceTimersByTime(600)
    })

    // Now router should have been called
    expect(mockRouterPush).toHaveBeenCalledWith(
      '/workout/rest-timer?between-sets=true'
    )
    expect(mockRouterPush).toHaveBeenCalledTimes(1)
  })

  it('should call nextSet() after navigation happens', async () => {
    const mockNextSet = vi.fn()
    vi.mocked(useWorkoutStore).mockReturnValue({
      exercises: [mockExercise],
      currentExerciseIndex: 0,
      currentSetIndex: 3,
      workoutSession: {
        id: 'test-session',
        startTime: new Date(),
        exercises: [],
      },
      nextSet: mockNextSet,
      setCurrentSetIndex: vi.fn(),
      nextExercise: vi.fn(),
      setCurrentExerciseById: vi.fn(),
      getCachedExerciseRecommendation: vi.fn().mockReturnValue(null),
    } as any)

    const { result } = renderHook(() => useSetScreenLogic(123))

    await waitFor(() => {
      expect(result.current.recommendation).toBeTruthy()
    })

    // Save set
    await act(async () => {
      result.current.handleSaveSet()
    })

    // Currently nextSet is called inside setTimeout
    expect(mockNextSet).not.toHaveBeenCalled()

    // Advance past the 600ms delay
    act(() => {
      vi.advanceTimersByTime(600)
    })

    // Now nextSet should have been called
    expect(mockNextSet).toHaveBeenCalledTimes(1)
  })
})
