import type { ExerciseModel, RecommendationModel } from '@/types'
import type { WorkoutLogSerieModelRef } from '@/types/api/WorkoutLogSerieModelRef'
import {
  roundToNearestIncrement,
  formatWeight,
  createMultiUnityWeight,
} from './weightHelpers'
import { WarmupCalculator } from './warmupCalculator'
import type { WarmupCalculationConfig } from './warmupCalculator'

// Forward declarations for ESLint
/* eslint-disable @typescript-eslint/no-use-before-define */

/**
 * Create base work set with common properties
 */
function createBaseWorkSet(
  exercise: ExerciseModel,
  recommendation: RecommendationModel,
  setNumber: number,
  existingSetsCount: number,
  isKg: boolean
): WorkoutLogSerieModelRef {
  return {
    ExerciseName: exercise.Label,
    ExerciseId: exercise.Id,
    Id: -(2000 + setNumber + existingSetsCount), // Unique negative ID for each set
    IsWarmups: false,
    SetNo: `${setNumber + 1}`,
    IsHeaderCell: existingSetsCount === 0 && setNumber === 0,
    IsFirstWorkSet: setNumber === 0,
    IsFinished: false,
    IsNext: false,
    Weight: recommendation.Weight,
    Reps: recommendation.Reps,
    SetTitle: '',
    LastTimeSet:
      setNumber === 0 &&
      recommendation.FirstWorkSetReps &&
      recommendation.FirstWorkSetWeight
        ? `Last time: ${recommendation.FirstWorkSetReps} x ${formatWeight(recommendation.FirstWorkSetWeight, isKg)}`
        : '',
    IsTimeBased: exercise.IsTimeBased,
    IsBodyweight: exercise.IsBodyweight,
    IsNormalset: exercise.IsNormalSets,
    Increments: recommendation.Increments || { Kg: 2.5, Lb: 5 },
    Min: recommendation.Min || { Kg: 0, Lb: 0 },
    Max: recommendation.Max || { Kg: 1000, Lb: 2000 },
    BodypartId: exercise.BodyPartId,
    IsUnilateral: exercise.IsUnilateral,
    IsFlexibility: exercise.IsFlexibility,
    EquipmentId: exercise.EquipmentId,
    IsActive: false,
    IsEditing: false,
    IsFirstSide: false,
    IsLastWarmupSet: false,
    IsFirstSetFinished: false,
    IsExerciseFinished: false,
    IsJustSetup: false,
    ShouldUpdateIncrement: false,
    IsBackOffSet: false,
    IsNextBackOffSet: false,
    IsDropSet: recommendation.IsDropSet || false,
    IsMaxChallenge: false,
    IsAssisted: exercise.IsAssisted,
    IsTimerOff: false,
    IsSizeChanged: false,
    ShowWorkTimer: false,
    NbPause: 0,
    OneRMProgress: 0,
    PreviousReps: 0,
    PreviousWeight: { Kg: 0, Lb: 0 },
    Speed: 0,
    HeaderImage: '',
    HeaderTitle: '',
    VideoUrl: exercise.VideoUrl,
    ShowPlusTooltip: false,
    ShowSuperSet3: false,
    ShowSuperSet2: false,
    IsLastSet: false,
    get BackColor() {
      return 'transparent'
    },
    get WeightSingal() {
      return formatWeight(this.Weight, isKg)
    },
    get WeightDouble() {
      return formatWeight(this.Weight, isKg)
    },
  } as WorkoutLogSerieModelRef
}

/**
 * Creates workout sets based on exercise and recommendation model
 * Exactly matches the MAUI CreateWorkoutSets functionality
 */
export function createWorkoutSets(
  exercise: ExerciseModel,
  recommendation: RecommendationModel,
  isKg: boolean
): WorkoutLogSerieModelRef[] {
  const setList: WorkoutLogSerieModelRef[] = []

  // 1. Generate Warm-up Sets - ALWAYS compute locally using WarmupsCount
  if (recommendation.WarmupsCount > 0) {
    // Use local warmup calculation - never rely on API WarmUpsList
    const config: WarmupCalculationConfig = {
      warmupsCount: recommendation.WarmupsCount,
      workingWeight: recommendation.Weight || { Kg: 0, Lb: 0 },
      workingReps: recommendation.Reps || 0,
      incrementValue: isKg ? (recommendation.Increments?.Kg || 2.5) : (recommendation.Increments?.Lb || 5),
      minWeight: recommendation.Min?.Kg,
      maxWeight: recommendation.Max?.Kg,
      isPlateAvailable: false, // Default - could be enhanced later
      isDumbbellAvailable: false,
      isBandsAvailable: false,
      isPulleyAvailable: false,
      isBodyweight: exercise.IsBodyweight || false,
      isWeighted: exercise.IsWeighted || false,
      isAssisted: exercise.IsAssisted || false,
      barbellWeight: isKg ? 20 : 45, // Standard barbell weight
      availablePlates: '', // Default empty
      userBodyWeight: 80, // Default - could be passed as parameter
      isKg
    }
    const calculatedWarmups = WarmupCalculator.computeWarmups(config)

    for (let i = 0; i < calculatedWarmups.length; i++) {
      const warmup = calculatedWarmups[i]
      const warmupSet = {
        Id: -(1000 + i), // Unique negative ID for warmup sets
        ExerciseId: exercise.Id,
        Weight: { Kg: 0, Lb: 0 }, // Warmup sets should not have work set weights
        WarmUpWeightSet: warmup.WarmUpWeightSet,
        IsWarmups: true,
        WarmUpReps: warmup.WarmUpReps,
        Reps: 0, // Required by interface but not used for warmups
        PreviousReps: warmup.WarmUpReps,
        SetNo: 'W',
        IsLastWarmupSet: i === calculatedWarmups.length - 1,
        IsHeaderCell: i === 0,
        HeaderImage: '',
        HeaderTitle: '',
        ExerciseName: exercise.Label,
        EquipmentId: exercise.EquipmentId,
        SetTitle: (() => {
          if (i === 0) return "Let's warm up:"
          if (i === calculatedWarmups.length - 1)
            return 'Last warm-up set:'
          return ''
        })(),
        IsFinished: false,
        IsNext: i === 0,
        LastTimeSet: '',
        IsTimeBased: exercise.IsTimeBased,
        IsBodyweight: exercise.IsBodyweight,
        IsNormalset: exercise.IsNormalSets,
        Increments: recommendation.Increments || { Kg: 2.5, Lb: 5 },
        Min: recommendation.Min || { Kg: 0, Lb: 0 },
        Max: recommendation.Max || { Kg: 1000, Lb: 2000 },
        BodypartId: exercise.BodyPartId,
        IsUnilateral: exercise.IsUnilateral,
        IsFlexibility: exercise.IsFlexibility,
        IsActive: false,
        IsEditing: false,
        IsFirstSide: false,
        IsFirstSetFinished: false,
        IsFirstWorkSet: false,
        IsExerciseFinished: false,
        IsJustSetup: false,
        ShouldUpdateIncrement: false,
        IsBackOffSet: false,
        IsNextBackOffSet: false,
        IsDropSet: recommendation.IsDropSet || false,
        IsMaxChallenge: false,
        IsAssisted: exercise.IsAssisted,
        IsTimerOff: false,
        IsSizeChanged: false,
        ShowWorkTimer: false,
        NbPause: 0,
        OneRMProgress: 0,
        PreviousWeight: { Kg: 0, Lb: 0 },
        Speed: 0,
        VideoUrl: exercise.VideoUrl,
        ShowPlusTooltip: false,
        ShowSuperSet3: false,
        ShowSuperSet2: false,
        IsLastSet: false,
        get BackColor() {
          return 'transparent'
        },
        get WeightSingal() {
          return formatWeight(warmup.WarmUpWeightSet, isKg)
        },
        get WeightDouble() {
          return formatWeight(warmup.WarmUpWeightSet, isKg)
        },
      } as WorkoutLogSerieModelRef
      setList.push(warmupSet)
    }
  }

  // 2. Generate Work Sets based on type
  // eslint-disable-next-line @typescript-eslint/no-use-before-define
  if (recommendation.IsPyramid) {
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    generatePyramidSets(exercise, recommendation, setList, isKg)
  } else if (recommendation.IsReversePyramid) {
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    generateReversePyramidSets(exercise, recommendation, setList, isKg)
  } else if (recommendation.IsBackOffSet) {
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    generateBackOffSets(exercise, recommendation, setList, isKg)
  } else if (recommendation.IsDropSet) {
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    generateDropSets(exercise, recommendation, setList, isKg)
  } else if (recommendation.NbPauses > 0) {
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    generateRestPauseSets(exercise, recommendation, setList, isKg)
  } else {
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    generateNormalSets(exercise, recommendation, setList, isKg)
  }

  // 3. Set additional properties for all sets
  setList.forEach((set) => {
    set.IsBodyweight = exercise.IsBodyweight
    set.IsTimeBased = exercise.IsTimeBased
    set.IsUnilateral = exercise.IsUnilateral
    set.Increments = recommendation.Increments || { Kg: 2.5, Lb: 5 }
    set.Min = recommendation.Min || { Kg: 0, Lb: 0 }
    set.Max = recommendation.Max || { Kg: 1000, Lb: 2000 }
    set.IsFlexibility = exercise.IsFlexibility
    set.EquipmentId = exercise.EquipmentId
  })

  // 4. Calculate IsNext for unfinished sets
  const firstUnfinishedIndex = setList.findIndex((set) => !set.IsFinished)
  if (firstUnfinishedIndex !== -1 && setList[firstUnfinishedIndex]) {
    setList[firstUnfinishedIndex].IsNext = true
  }

  return setList
}

/**
 * Generate pyramid sets (weight increases, reps decrease)
 */
function generatePyramidSets(
  exercise: ExerciseModel,
  recommendation: RecommendationModel,
  setList: WorkoutLogSerieModelRef[],
  isKg: boolean
): void {
  for (let j = 0; j < recommendation.Series; j++) {
    const rec = createBaseWorkSet(
      exercise,
      recommendation,
      j,
      setList.length,
      isKg
    )

    if (j === 0) {
      // First work set
      rec.SetTitle = recommendation.Series > 1 ? 'Pyramid set:' : ''
    } else {
      // Subsequent pyramid sets
      const lastSet = setList[setList.length - 1]
      if (lastSet?.Weight) {
        const weightIncrease = lastSet.Weight.Kg * 0.1 // 10% increase
        const newWeight = lastSet.Weight.Kg + weightIncrease

        rec.Weight = createMultiUnityWeight(
          roundToNearestIncrement(
            newWeight,
            recommendation.Increments?.Kg || 2.5
          ),
          'kg'
        )
        rec.Reps = Math.max(1, (lastSet.Reps || 0) - 2) // 2 reps less
        rec.SetTitle = ''
      }
    }

    rec.IsLastSet = j === recommendation.Series - 1
    setList.push(rec)
  }
}

/**
 * Generate reverse pyramid sets (weight decreases, reps increase)
 */
function generateReversePyramidSets(
  exercise: ExerciseModel,
  recommendation: RecommendationModel,
  setList: WorkoutLogSerieModelRef[],
  isKg: boolean
): void {
  for (let j = 0; j < recommendation.Series; j++) {
    const rec = createBaseWorkSet(
      exercise,
      recommendation,
      j,
      setList.length,
      isKg
    )

    if (j === 0) {
      // First work set (heaviest)
      rec.SetTitle = recommendation.Series > 1 ? 'Reverse pyramid:' : ''
    } else {
      // Subsequent reverse pyramid sets
      const lastSet = setList[setList.length - 1]
      if (lastSet?.Weight) {
        const weightDecrease = lastSet.Weight.Kg * 0.1 // 10% decrease
        const newWeight = lastSet.Weight.Kg - weightDecrease

        rec.Weight = createMultiUnityWeight(
          roundToNearestIncrement(
            newWeight,
            recommendation.Increments?.Kg || 2.5
          ),
          'kg'
        )
        rec.Reps = (lastSet.Reps || 0) + 2 // 2 reps more
        rec.SetTitle = ''
      }
    }

    rec.IsLastSet = j === recommendation.Series - 1
    setList.push(rec)
  }
}

/**
 * Generate back-off sets (lighter sets after main set)
 */
function generateBackOffSets(
  exercise: ExerciseModel,
  recommendation: RecommendationModel,
  setList: WorkoutLogSerieModelRef[],
  isKg: boolean
): void {
  for (let j = 0; j < recommendation.Series; j++) {
    const rec = createBaseWorkSet(
      exercise,
      recommendation,
      j,
      setList.length,
      isKg
    )

    if (j === 0) {
      // Main set
      rec.SetTitle = ''
    } else {
      // Back-off sets (20% lighter)
      rec.Weight =
        recommendation.BackOffSetWeight ||
        createMultiUnityWeight(recommendation.Weight.Kg * 0.8, 'kg')
      rec.Reps = recommendation.Reps + 2
      rec.SetTitle = 'Back-off set:'
      rec.IsBackOffSet = true
    }

    rec.IsLastSet = j === recommendation.Series - 1
    setList.push(rec)
  }
}

/**
 * Generate rest-pause set
 */
function generateRestPauseSets(
  exercise: ExerciseModel,
  recommendation: RecommendationModel,
  setList: WorkoutLogSerieModelRef[],
  isKg: boolean
): void {
  const rec = createBaseWorkSet(
    exercise,
    recommendation,
    0,
    setList.length,
    isKg
  )

  rec.SetTitle = 'Rest-pause'
  rec.NbPause = recommendation.NbPauses
  rec.IsFirstWorkSet = true
  rec.IsLastSet = true

  setList.push(rec)
}

/**
 * Generate drop sets
 */
function generateDropSets(
  exercise: ExerciseModel,
  recommendation: RecommendationModel,
  setList: WorkoutLogSerieModelRef[],
  isKg: boolean
): void {
  for (let j = 0; j < recommendation.Series; j++) {
    const rec = createBaseWorkSet(
      exercise,
      recommendation,
      j,
      setList.length,
      isKg
    )

    rec.SetTitle = 'Drop set'
    rec.IsDropSet = true
    rec.IsLastSet = j === recommendation.Series - 1

    setList.push(rec)
  }
}

/**
 * Generate normal work sets
 */
function generateNormalSets(
  exercise: ExerciseModel,
  recommendation: RecommendationModel,
  setList: WorkoutLogSerieModelRef[],
  isKg: boolean
): void {
  for (let j = 0; j < recommendation.Series; j++) {
    const rec = createBaseWorkSet(
      exercise,
      recommendation,
      j,
      setList.length,
      isKg
    )

    rec.SetTitle = j === 0 && recommendation.Series > 1 ? 'Working sets:' : ''
    rec.IsNormalset = true
    rec.IsLastSet = j === recommendation.Series - 1

    setList.push(rec)
  }
}
